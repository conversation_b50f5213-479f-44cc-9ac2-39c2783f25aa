import os
import re
import torch
import torch.nn.functional as F
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import GNN
from GNN import tfidf, glove_dict, embedding_dim, TextHGTClassifier, PerplexityCalculator, en_dict

DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# GPT-2 perplexity 计算器
en_pc = PerplexityCalculator(
    model_dir="/root/zhihu_ai_scraper_python/gpt2_local",
    device=DEVICE
)
zh_pc = PerplexityCalculator(
    model_dir="/root/zhihu_ai_scraper_python/gpt2_chinese_local",
    device=DEVICE
)

# Monkey-patch 原始 build_hetero_graph 支持 use_perp 和 use_bilingual
_orig_build = GNN.build_hetero_graph

def build_hetero_graph(item,
                       tfidf_vect,
                       glove_dict_in,
                       embedding_dim,
                       vocab=None,
                       syntax_edges=None,
                       use_perp=True,
                       use_bilingual=True):
    # 复制输入并按需修改
    item_mod = dict(item)
    if not use_perp:
        item_mod['perp'] = 1.0
    # 只用英文时，替换 glove_dict 为 en_dict
    glove_dict_mod = en_dict if not use_bilingual else glove_dict_in

    # 调用原始构建函数
    data = _orig_build(
        item_mod,
        tfidf_vect,
        glove_dict_mod,
        embedding_dim,
        vocab,
        syntax_edges
    )

    # —— 统一转换：针对 HeteroData 中每种关系，强制 edge_index/edge_type 为 int64 ——
    try:
        # 单一类型图
        data.edge_index = data.edge_index.long()
    except:
        pass
    try:
        for rel in data.edge_types:
            sub = data[rel]
            sub.edge_index = sub.edge_index.long()
            if hasattr(sub, 'edge_type'):
                sub.edge_type = sub.edge_type.long()
    except:
        pass

    return data

# 覆盖原函数
GNN.build_hetero_graph = build_hetero_graph


def train_eval_hetero(
    csv_path: str,
    use_perp: bool = True,
    use_bilingual: bool = True,
    epochs: int = 5,
    batch_size: int = 32,
    device: str = DEVICE
) -> dict:
    # 1. 读取数据 & 计算 perplexity
    df = pd.read_csv(csv_path)
    texts = df['text'].astype(str).tolist()
    labels = df['generated'].astype(int).tolist()
    perps = []
    for t in texts:
        p = zh_pc.calc(t) if re.search(r"[\u4e00-\u9fff]", t) else en_pc.calc(t)
        perps.append(p if use_perp else 1.0)

    # 2. 构建 items 列表
    items = [
        {'text': txt, 'label': lbl, 'perp': p}
        for txt, lbl, p in zip(texts, labels, perps)
    ]

    # 3. 划分 train/test
    from sklearn.model_selection import train_test_split
    train_items, test_items = train_test_split(
        items, test_size=0.3, stratify=labels, random_state=42
    )

    # 4. 初始化模型
    in_dim = embedding_dim + 2
    model = TextHGTClassifier(
        metadata=(['text','word'], [('text','contains','word')]),
        in_dim=in_dim,
        hid=256, n_rels=1, heads=2, layers=3, n_out=2
    ).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)

    # 5. 训练循环
    for ep in range(1, epochs+1):
        total_loss = 0
        for item in train_items:
            graph = build_hetero_graph(
                item, tfidf, glove_dict, embedding_dim,
                vocab=None, syntax_edges=None,
                use_perp=use_perp, use_bilingual=use_bilingual
            ).to(device)
            optimizer.zero_grad()
            logits = model(graph)
            # 如果 logits 是一维张量，增加批次维度，匹配 target 形状
            if logits.dim() == 1:
                logits = logits.unsqueeze(0)
            loss = F.cross_entropy(logits, graph['text'].y.view(-1))
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        print(f"Epoch {ep}/{epochs} loss: {total_loss/len(train_items):.4f}")

    # 6. 训练后评估
    return eval_hetero(
        test_items,
        use_perp=use_perp,
        use_bilingual=use_bilingual,
        batch_size=batch_size,
        device=device
    )


def eval_hetero(
    items: list,
    use_perp: bool = True,
    use_bilingual: bool = True,
    batch_size: int = 32,
    device: str = DEVICE
) -> dict:
    # 初始化模型
    in_dim = embedding_dim + 2
    model = TextHGTClassifier(
        metadata=(['text','word'], [('text','contains','word')]),
        in_dim=in_dim,
        hid=256, n_rels=1, heads=2, layers=3, n_out=2
    ).to(device)

    # 批量预测
    all_preds, all_labels, all_probs = [], [], []
    for item in items:
        graph = build_hetero_graph(
            item, tfidf, glove_dict, embedding_dim,
            vocab=None, syntax_edges=None,
            use_perp=use_perp, use_bilingual=use_bilingual
        ).to(device)
        with torch.no_grad():
            logits = model(graph)
            probs = torch.softmax(logits, dim=1)[:,1].cpu().numpy()
        pred = logits.argmax(dim=1).cpu().item()
        all_preds.append(pred)
        all_labels.append(item['label'])
        all_probs.append(probs)

    # 7. 计算指标
    return {
        'accuracy':  accuracy_score(all_labels, all_preds),
        'precision': precision_score(all_labels, all_preds, average='macro'),
        'recall':    recall_score(all_labels, all_preds, average='macro'),
        'f1':        f1_score(all_labels, all_preds, average='macro'),
        'auc':       roc_auc_score(all_labels, all_probs)
    }

# 导出接口
__all__ = ['train_eval_hetero', 'eval_hetero']
