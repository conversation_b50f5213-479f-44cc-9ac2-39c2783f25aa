# merge_hc_bilingual.py
from datasets import load_dataset
import pandas as pd

# —— 1) 加载中文 all 配置 —— #
ds_zh = load_dataset(
    "Hello-SimpleAI/HC3-Chinese",
    "all",                # 指定 config
    split="all",
    trust_remote_code=True
)

# 展平中文回答列表，分别标记 human=0, chatgpt=1
records_zh = []
for row in ds_zh:
    for ans in row["human_answers"]:
        records_zh.append({"text": ans, "generated": 0})
    for ans in row["chatgpt_answers"]:
        records_zh.append({"text": ans, "generated": 1})
df_zh = pd.DataFrame(records_zh)

# —— 2) 加载英文 all 配置 —— #
ds_en = load_dataset(
    "Hello-SimpleAI/HC3",
    "all",
    split="train",
    trust_remote_code=True
)

# 展平英文回答列表
records_en = []
for row in ds_en:
    for ans in row["human_answers"]:
        records_en.append({"text": ans, "generated": 0})
    for ans in row["chatgpt_answers"]:
        records_en.append({"text": ans, "generated": 1})
df_en = pd.DataFrame(records_en)

# —— 3) 合并并打乱顺序 —— #
df = pd.concat([df_zh, df_en], ignore_index=True)\
       .sample(frac=1, random_state=42)

# —— 4) 保存为 CSV —— #
df.to_csv("hc3_bilingual.csv", index=False, encoding="utf-8")
print(f"合并后样本总数：{len(df)}，已保存到 hc3_bilingual.csv")
