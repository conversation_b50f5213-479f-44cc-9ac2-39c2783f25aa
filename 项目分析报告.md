# 基于图神经网络的AI生成文本检测系统 - 项目分析报告

## 📋 项目概述

本项目是一个基于**图神经网络（GNN）的AI生成文本检测系统**，专门用于识别中英文混合文本是否由AI生成。项目的核心创新在于结合了**困惑度（Perplexity）特征**和**异构图注意力网络（HGT）**来提升检测准确性。

### 🎯 主要目标
- 准确识别AI生成的中英文文本
- 支持中英文混合语言环境
- 提供可解释的检测结果
- 建立完整的实验评估体系

## 🏗️ 技术架构

### 1. 数据处理模块

#### 数据源
- **HC3数据集**：中英文双语AI生成文本检测数据集
- **数据规模**：包含人类撰写和ChatGPT生成的文本对

#### 预处理流程
```python
# 中英文混合分词器
def mixed_tokenizer(text: str):
    frags = re.findall(r"[A-Za-z0-9_]+|[\u4e00-\u9fff]+", text)
    tokens = []
    for frag in frags:
        if re.match(r"[A-Za-z0-9_]+", frag):  # 英文或数字
            tokens.append(frag.lower())
        else:  # 汉字块
            tokens.extend(jieba.lcut(frag))  # 用jieba切分
    return tokens
```

- **TF-IDF特征提取**：最大5000个特征
- **困惑度计算**：使用GPT-2模型

### 2. 困惑度计算系统

#### 双语支持架构
```python
class PerplexityCalculator:
    def __init__(self, model_dir: str, device: str = 'cpu'):
        self.tokenizer = AutoTokenizer.from_pretrained(model_dir, local_files_only=True)
        self.model = AutoModelForCausalLM.from_pretrained(model_dir, local_files_only=True).to(device)
        
    def calc(self, text: str) -> float:
        enc = self.tokenizer(text, return_tensors='pt', truncation=True, max_length=512).to(self.device)
        with torch.no_grad():
            loss = self.model(**enc, labels=enc['input_ids']).loss
        return float(torch.exp(loss))
```

#### 特性
- **英文模型**：GPT-2 本地版本
- **中文模型**：GPT-2-Chinese 本地版本
- **自动语言检测**：根据文本中汉字自动选择对应模型
- **批量处理**：支持高效的批量困惑度计算

### 3. 异构图构建

#### 图结构设计
```python
def build_hetero_graph(item, tfidf_vect, glove_dict, embedding_dim):
    data = HeteroData()
    
    # 1. 计算归一化困惑度
    lp = math.log(perp + 1.0)
    lp_clamped = max(0.0, min(lp / 10.0, 1.0))
    
    # 2. 构建词节点特征
    glove_vecs = []
    for word in tfidf_vect.get_feature_names_out():
        vec = glove_dict.get(word, torch.zeros(embedding_dim))
        glove_vecs.append(vec)
    data['word'].x = torch.stack(glove_vecs, dim=0)
    
    # 3. 构建文本-词边关系
    vec = tfidf_vect.transform([text]).tocoo()
    weights = torch.tensor(vec.data * lp_clamped, dtype=torch.float)
    data['text', 'contains', 'word'].edge_weight = weights
    
    return data
```

#### 核心特性
- **节点类型**：文本节点（text）和词节点（word）
- **边关系**：文本-词包含关系（text-contains-word）
- **权重设计**：TF-IDF权重 × 困惑度权重
- **特征融合**：困惑度特征与词向量特征结合

### 4. 图神经网络模型

#### HGT（异构图Transformer）架构
```python
class HGTConv(MessagePassing):
    def __init__(self, in_dim, out_dim, num_types, num_relations, n_heads):
        super(HGTConv, self).__init__()
        self.k_linears = nn.ModuleList()  # Key变换
        self.q_linears = nn.ModuleList()  # Query变换  
        self.v_linears = nn.ModuleList()  # Value变换
        self.a_linears = nn.ModuleList()  # 输出变换
        
    def message(self, edge_index_i, node_inp_i, node_inp_j, node_type_i, node_type_j, edge_type):
        # 异构注意力机制
        for source_type in range(self.num_types):
            for target_type in range(self.num_types):
                for relation_type in range(self.num_relations):
                    # 计算注意力权重和消息
                    q_mat = self.q_linears[target_type](target_node_vec)
                    k_mat = self.k_linears[source_type](source_node_vec)
                    v_mat = self.v_linears[source_type](source_node_vec)
```

#### 模型特点
- **多头注意力**：增强模型表达能力
- **类型感知**：针对不同节点类型使用不同的变换矩阵
- **关系建模**：显式建模不同类型的边关系
- **层次化设计**：3层GNN + 分类器

### 5. 词向量系统

#### 多语言词向量融合
```python
# 加载英文GloVe词向量
en_dict, embedding_dim = load_glove_txt('glove.6B.200d.txt')

# 加载中文腾讯词向量
cn_wv = KeyedVectors.load_word2vec_format('light_Tencent_AILab_ChineseEmbedding.bin', binary=True)
cn_dict = {w: torch.tensor(cn_wv[w], dtype=torch.float) for w in cn_wv.key_to_index}

# 融合策略：中文覆盖英文
glove_dict = {**en_dict, **cn_dict}
```

#### 特性
- **英文**：GloVe 200维预训练词向量
- **中文**：腾讯AI Lab 200维预训练词向量
- **融合策略**：中文词向量优先，处理中英文重叠词汇
- **维度统一**：统一200维特征空间

## 🚀 核心创新点

### 1. 困惑度驱动的图构建
- **创新思路**：将困惑度作为图边权重，增强图的表达能力
- **技术实现**：`weight = tfidf_weight × normalized_perplexity`
- **效果**：提升模型对AI生成文本特征的敏感性

### 2. 中英文双语统一处理
- **语言检测**：自动识别文本语言并选择对应的困惑度模型
- **特征空间统一**：中英文词向量映射到同一特征空间
- **分词策略**：英文空格分割 + 中文jieba分词

### 3. 异构图注意力机制
- **节点异构性**：文本节点和词节点具有不同的特征和语义
- **关系建模**：显式建模文本-词包含关系
- **注意力机制**：类型感知的多头注意力

### 4. 消融实验支持
```python
def build_hetero_graph(item, use_perp=True, use_bilingual=True):
    if not use_perp:
        item_mod['perp'] = 1.0  # 禁用困惑度特征
    glove_dict_mod = en_dict if not use_bilingual else glove_dict_in  # 单语/双语切换
```

## 🧪 实验设计

### 1. 对比实验设置

| 模型 | 描述 | 特征 |
|------|------|------|
| TF-IDF + LR | 传统机器学习基线 | TF-IDF特征 + 逻辑回归 |
| mBERT微调 | 深度学习基线 | 多语言BERT微调 |
| 简化HeteroGNN | 消融实验 | 无困惑度 + 单语言 |
| PD-HeteroGNN | 完整模型 | 困惑度驱动 + 双语支持 |

### 2. 评估指标
- **分类指标**：Accuracy、Precision、Recall、F1-Score
- **排序指标**：AUC（ROC曲线下面积）
- **可视化**：混淆矩阵、ROC曲线、PR曲线

### 3. 实验结果分析
```python
# 示例结果（来自代码注释）
"""
Test F1:     0.9543770357943299
Precision:   0.9491107521469097  
Recall:      0.9607613818338262
"""
```

## 💻 技术特色

### 1. 流式训练支持
```python
def train_streaming(csv_path, batch_sz=8, chunk_size=2000, epochs_per_chunk=1):
    reader = pd.read_csv(csv_path, iterator=True, chunksize=chunk_size)
    
    # 断点恢复机制
    ckpt_resume = os.path.join(CKPT_DIR, 'interrupt.pth')
    if os.path.exists(ckpt_resume):
        model.load_state_dict(torch.load(ckpt_resume))
        
    try:
        for chunk_idx, df_chunk in enumerate(reader):
            # 分块训练逻辑
            pass
    except KeyboardInterrupt:
        torch.save(model.state_dict(), ckpt_resume)  # 保存中断状态
```

### 2. 完整的可视化系统
- **训练监控**：损失曲线、验证F1曲线
- **数据分析**：困惑度分布、样本分布统计
- **结果展示**：混淆矩阵、ROC/PR曲线

### 3. 鲁棒性设计
- **梯度裁剪**：防止梯度爆炸
- **早停机制**：防止过拟合
- **学习率调度**：ReduceLROnPlateau
- **异常处理**：完善的错误恢复机制

## 📁 项目结构

```
GNN/
├── 核心模块
│   ├── GNN.py              # 主训练脚本
│   ├── model.py            # 模型定义（分类器、匹配器）
│   ├── conv.py             # 图卷积层实现（HGT、DenseHGT）
│   └── GNN_refactor.py     # 重构版本（支持消融实验）
├── 实验脚本  
│   ├── run_exp.py          # 实验对比脚本
│   ├── merge_hc.py         # HC3数据集合并脚本
│   └── perplex_fea.py      # 特征分析脚本
├── 数据文件
│   ├── hc3_bilingual.csv   # 中英文混合数据集
│   ├── AI_Human.csv        # 标注数据
│   └── test.csv            # 测试数据
├── 预训练模型
│   ├── bert-base-multilingual-cased/  # mBERT模型
│   ├── gpt2_local/         # 英文GPT-2模型
│   ├── gpt2_chinese_local/ # 中文GPT-2模型
│   └── glove.6B/           # GloVe词向量
├── 分析结果
│   ├── analysis/           # 特征分析结果
│   ├── figures/            # 可视化图表
│   └── checkpoints/        # 模型检查点
└── 文档
    ├── 代码说明.docx       # 项目说明文档
    ├── 算法对比.xlsx       # 算法对比结果
    └── *.pdf               # 相关论文文档
```

## 🎯 实际应用价值

### 1. AI内容检测
- **应用场景**：检测ChatGPT、GPT-4等大语言模型生成的文本
- **技术优势**：高准确率（F1>95%）、支持中英文混合

### 2. 学术诚信保障
- **目标用户**：教育机构、学术期刊
- **功能**：识别学术论文、作业中的AI生成内容
- **特色**：可解释的检测结果

### 3. 内容审核平台
- **应用领域**：社交媒体、新闻平台、内容创作平台
- **价值**：自动化AI生成内容识别和标记
- **优势**：实时检测、批量处理

### 4. 多语言环境支持
- **适用场景**：国际化平台、跨语言内容检测
- **技术特点**：统一的中英文处理框架
- **扩展性**：可扩展到其他语言

## 🔬 技术深度分析

### 1. 模型复杂度
- **参数量**：约256×3层GNN + 分类器
- **计算复杂度**：O(|E|×d×h) （边数×特征维度×注意力头数）
- **内存需求**：支持流式训练，适应大规模数据

### 2. 性能优化
- **批处理**：支持批量困惑度计算
- **缓存机制**：词向量预加载
- **GPU加速**：CUDA支持

### 3. 可扩展性
- **新语言支持**：添加对应的困惑度模型和词向量
- **新特征集成**：模块化设计便于添加新特征
- **模型架构**：支持不同的GNN架构（GAT、GCN等）

## 📊 项目评估

### 优势
1. **技术创新性**：困惑度驱动的异构图神经网络
2. **实用性强**：高准确率的AI文本检测
3. **多语言支持**：中英文统一处理框架
4. **完整性**：从数据处理到模型部署的完整流程
5. **可解释性**：基于图结构的可解释检测

### 技术挑战
1. **计算复杂度**：图神经网络训练成本较高
2. **数据依赖**：需要高质量的标注数据
3. **模型泛化**：对新型AI模型的适应性
4. **实时性**：大规模部署的响应时间优化

### 改进方向
1. **模型轻量化**：知识蒸馏、模型压缩
2. **增量学习**：适应新的AI生成模型
3. **多模态扩展**：支持图像、音频等多模态内容
4. **联邦学习**：隐私保护的分布式训练

## 🎉 总结

本项目展现了深度学习、图神经网络和自然语言处理的综合应用，是一个技术含量较高的AI检测系统。通过创新性地结合困惑度特征和异构图注意力网络，在AI生成文本检测任务上取得了优异的性能表现。项目具有重要的学术价值和实际应用前景，为AI内容检测领域提供了新的技术思路和解决方案。
