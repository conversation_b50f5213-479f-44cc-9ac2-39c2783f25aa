import pandas as pd
import matplotlib.pyplot as plt
import os
import seaborn as sns

# 1) 读特征
df = pd.read_csv("analysis/features.csv")

# 2) 按标签分组
df_human = df[df.label == 0]
df_ai    = df[df.label == 1]

os.makedirs("analysis/plots", exist_ok=True)

# 3) Perplexity 分布对比
fig, ax = plt.subplots()
sns.boxplot(x="label", y="perplexity", data=df, ax=ax)
ax.set_xticks([0, 1])
ax.set_xticklabels(["Human","AI"])
ax.set_title("Perplexity Distribution: Human vs AI")
fig.tight_layout()
fig.savefig("analysis/plots/perplexity_compare.png")
plt.close(fig)

# 4) TF–IDF 分布对比
fig, ax = plt.subplots()
sns.boxplot(x="label", y="tfidf", data=df, ax=ax)
ax.set_xticks([0, 1])
ax.set_xticklabels(["Human","AI"])
ax.set_title("TF–IDF Weight Distribution: Human vs AI")
fig.tight_layout()
fig.savefig("analysis/plots/tfidf_compare.png")
plt.close(fig)

# 5) Combined 分布对比
fig, ax = plt.subplots()
sns.boxplot(x="label", y="combined", data=df, ax=ax)
ax.set_xticks([0, 1])
ax.set_xticklabels(["Human","AI"])
ax.set_title("TF–IDF × Perplexity Distribution: Human vs AI")
fig.tight_layout()
fig.savefig("analysis/plots/combined_compare.png")
plt.close(fig)

print("Plots saved to analysis/plots/")
