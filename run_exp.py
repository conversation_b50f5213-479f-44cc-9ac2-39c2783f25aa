import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.pipeline import make_pipeline
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
from torch.utils.data import Dataset
import torch,os,json
from sklearn.metrics import precision_score, f1_score, recall_score, roc_auc_score, accuracy_score
from GNN_refactor import train_eval_hetero, eval_hetero  # 你需要把 train()/eval() 封装成可接参数的小函数
from transformers import BertTokenizer

# 持久化实验结果文件
RESULT_JSON = '/root/zhihu_ai_scraper_python/results.json'

# 加载已保存的实验结果
if os.path.exists(RESULT_JSON):
    with open(RESULT_JSON, 'r', encoding='utf-8') as f:
        results = json.load(f)
else:
    results = []

def save_results():
    # 写回 JSON
    with open(RESULT_JSON, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    # 同步保存 CSV 方便查看
    pd.DataFrame(results).to_csv('results.csv', index=False)
# 1. 读数据
df = pd.read_csv('hc3_bilingual.csv')
texts = df['text'].astype(str).tolist()
labels = df['generated'].astype(int).tolist()

# 2. 划分
# 1) 先划分出 70% 训练集，30% 临时集
X_tr, X_tmp, y_tr, y_tmp = train_test_split(
    texts, labels,
    train_size=0.7, stratify=labels, random_state=42
)

# 2) 再把临时集平均分成 15% 验证集 + 15% 测试集
#    这里 test_size=0.5 表示从 30% 临时集中再分出一半给测试集
X_val, X_te, y_val, y_te = train_test_split(
    X_tmp, y_tmp,
    test_size=0.5, stratify=y_tmp, random_state=42
)

results = []

# —— 实验 A：TF-IDF + LR Baseline ——
pipe = make_pipeline(
    TfidfVectorizer(tokenizer=lambda t: t.split(), max_features=5000),
    LogisticRegression(max_iter=1000)
)
pipe.fit(X_tr, y_tr)
y_pred = pipe.predict(X_te)
# 预测概率，用于 AUC
y_prob = pipe.predict_proba(X_te)[:, 1]
results.append({
    '版本': 'TF-IDF + LR',
    'Accuracy': accuracy_score(y_te, y_pred),
    'F1': f1_score(y_te, y_pred, average='macro'),
    'Recall':   recall_score(y_te, y_pred, average='macro'),
    'AUC':      roc_auc_score(y_te, y_prob)
})

# —— 实验 B：mBERT 微调 Baseline ——
class TextDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len=128):
        self.texts, self.labels = texts, labels
        self.tokenizer, self.max_len = tokenizer, max_len
    def __len__(self): return len(self.texts)
    def __getitem__(self, idx):
        enc = self.tokenizer(self.texts[idx],
                             truncation=True,
                             padding='max_length',
                             max_length=self.max_len,
                             return_tensors='pt')
        return {
            'input_ids': enc['input_ids'].squeeze(),
            'attention_mask': enc['attention_mask'].squeeze(),
            'labels': torch.tensor(self.labels[idx], dtype=torch.long)
        }

tokenizer = BertTokenizer.from_pretrained('./bert-base-multilingual-cased')
model = BertForSequenceClassification.from_pretrained('bert-base-multilingual-cased', num_labels=2)

train_ds = TextDataset(X_tr, y_tr, tokenizer)
eval_ds  = TextDataset(X_te, y_te, tokenizer)
args = TrainingArguments(
    output_dir='mbert_out', num_train_epochs=3, per_device_train_batch_size=8,
    per_device_eval_batch_size=16, evaluation_strategy='epoch', logging_steps=50
)
from scipy.special import softmax
def compute_mbert_metrics(p):
    preds = p.predictions.argmax(-1)
    probs = softmax(p.predictions, axis=1)[:, 1]
    return {
        'accuracy': accuracy_score(p.label_ids, preds),
        'f1':       f1_score(p.label_ids, preds, average='macro'),
        'recall':   recall_score(p.label_ids, preds, average='macro'),
        'auc':      roc_auc_score(p.label_ids, probs)
    }
trainer = Trainer(model=model,
                  args=args,
                  train_dataset=train_ds,
                  eval_dataset=eval_ds,
                  compute_metrics=compute_mbert_metrics)
metrics = trainer.evaluate()
results.append({
    '版本': 'mBERT 微调',
    'Accuracy': metrics['eval_accuracy'],
    'F1': metrics['eval_f1'],
    'Recall': metrics['eval_recall'],
    'AUC': metrics['eval_auc']
})

# —— 实验 C：简化 HeteroGNN（消融：无 perplexity + 单语） ——
# —— 第一步：训练简化版模型并保存 checkpoint ——
# train_eval_hetero 内部会保存模型到 'checkpoints/hetero_simplified.pth'
metrics_train = train_eval_hetero(
    csv_path='hc3_bilingual.csv',
    use_perp=False,
    use_bilingual=False,
    epochs=5,
    batch_size=32,
    device='cuda'
)
print("训练结束，Train 阶段结果（可忽略，只为确认流程）:", metrics_train)

# —— 第二步：加载刚才训练好的模型，做评估 ——
metrics_eval = eval_hetero(
    csv_path='hc3_bilingual.csv',
    checkpoint_path='hetero_simplified.pth',
    use_perp=False,
    use_bilingual=False,
    batch_size=32,
    device='cuda'
)
print("评估结果（包含 Accuracy, Precision, Recall, F1, AUC）:", metrics_eval)

# 然后你再把 metrics_eval 塞进最终 results 列表里
results.append({
    '版本':     '简化 HeteroGNN',
    'Accuracy':  metrics_eval['accuracy'],
    'Precision': metrics_eval['precision'],
    'Recall':    metrics_eval['recall'],
    'F1':        metrics_eval['f1'],
    'AUC':       metrics_eval['auc']
})

# —— 实验 D：完整 PD-HeteroGNN ——
metrics_full = eval_hetero(
    csv_path='hc3_bilingual.csv',
    checkpoint_path='final_model.pth',
    use_perp=True,
    use_bilingual=True,
    batch_size=32,
    device='cuda'
)
results.append({
    '版本':     '完整 PD-HeteroGNN',
    'Accuracy':  metrics_full['accuracy'],
    'Precision': metrics_full['precision'],
    'Recall':    metrics_full['recall'],
    'F1':        metrics_full['f1'],
    'AUC':       metrics_full['auc']
})

# —— 汇总输出
df_res = pd.DataFrame(results)
print(df_res.to_markdown(index=False))
# 你也可以把它保存成 CSV / Excel
df_res.to_csv('experiment_summary.csv', index=False)
