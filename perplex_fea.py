import os
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from GNN import calc_batch_perplexity, tfidf, build_hetero_graph
from tqdm import tqdm

# 1) 读原始 CSV
df = pd.read_csv("/root/zhihu_ai_scraper_python/hc3_bilingual.csv")
texts  = df['text'].astype(str).tolist()
labels = df['generated'].astype(int).tolist()

# 2) 先做 TF–IDF（复用脚本里同一个 tfidf 实例和 fit 过程）
tfidf.fit(texts)
tfidf_vals = tfidf.transform(texts).sum(axis=1).A1

# 3) **不用再 new PerplexityCalculator**，直接调用已经验证过的 calc_batch_perplexity
perps = []
for i in tqdm(range(0, len(texts), 8), desc="Perplexity"):  # batch_sz=8 示例
    batch = texts[i:i+8]
    perps.extend(calc_batch_perplexity(batch, batch_size=len(batch)))

# 4) 合并、存 CSV
df_feat = pd.DataFrame({
    "text": texts,
    "label": labels,
    "tfidf": tfidf_vals,
    "perplexity": perps,
})
df_feat["combined"] = df_feat["tfidf"] * df_feat["perplexity"]

os.makedirs("analysis", exist_ok=True)
df_feat.to_csv("analysis/features.csv", index=False)
print("Saved analysis/features.csv")