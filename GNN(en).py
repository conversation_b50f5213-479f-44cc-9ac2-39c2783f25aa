import os, sys
import math
import argparse

import torch
import torch.nn as nn

from tqdm import tqdm
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split

from transformers import AutoModelForCausalLM, AutoTokenizer
from torch_geometric.data import HeteroData, Batch
from torch.utils.data import DataLoader
from model import GNN, Classifier

import pandas as pd
from torch.utils.data import DataLoader
from torch_geometric.data import Batch
import torch.nn.functional as F


# from sentence_transformers import SentenceTransformer

# —— 全局加载区 ——
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# bert_encoder = SentenceTransformer('/root/zhihu_ai_scraper_python/all-MiniLM-L6-v2', local_files_only=True)

def load_glove_embeddings(path):
    """
    加载 GloVe 文件，返回 dict(word->vector) 和向量维度 embedding_dim
    """
    embedding_dim = None
    glove_dict = {}
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.rstrip().split()
            word = parts[0]
            vec = torch.tensor([float(x) for x in parts[1:]], dtype=torch.float)
            if embedding_dim is None:
                embedding_dim = vec.size(0)
            # 只保留正确维度的向量
            if vec.size(0) == embedding_dim:
                glove_dict[word] = vec
    return glove_dict, embedding_dim

# —————————————————————————————————————————
# 1. Perplexity 预处理
# —————————————————————————————————————————
class PerplexityCalculator:
    def __init__(self, model_name='gpt2', device='cpu'):
        MODEL_LOCAL_DIR = "/root/zhihu_ai_scraper_python/gpt2_local"
        self.tokenizer = AutoTokenizer.from_pretrained(
            MODEL_LOCAL_DIR, local_files_only=True
        )
        # Ensure GPT2 tokenizer has a pad token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        self.model = AutoModelForCausalLM.from_pretrained(
            MODEL_LOCAL_DIR, local_files_only=True
        ).to(device)
        # Align model's pad token id
        self.model.config.pad_token_id = self.model.config.eos_token_id
        self.model = AutoModelForCausalLM.from_pretrained(
            MODEL_LOCAL_DIR, local_files_only=True
        ).to(device)
        self.model.eval()
        self.device = device

    def calc(self, text: str) -> float:
        enc = self.tokenizer(
            text,
            return_tensors='pt',
            truncation=True,
            max_length=512
        ).to(self.device)
        with torch.no_grad():
            loss = self.model(**enc, labels=enc['input_ids']).loss
        return torch.exp(loss).item()

class TextDataset(torch.utils.data.Dataset):
    def __init__(self, texts, labels, perps, tfidf_vect, vocab=None, syntax_edges=None):
        assert len(texts) == len(labels) == len(perps)
        self.texts = texts
        self.labels = labels
        self.perps = perps
        self.tfidf = tfidf_vect
        self.vocab = vocab
        self.syntax = syntax_edges

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        return {
            'text': self.texts[idx],
            'label': self.labels[idx],
            'perp': self.perps[idx]
        }

# —————————————————————————————————————————
# 2. 构建 HeteroData
# —————————————————————————————————————————
def build_hetero_graph(item, tfidf_vect, glove_dict, embedding_dim, vocab=None, syntax_edges=None):
    text = item['text']
    perp = item['perp']
    data = HeteroData()

    # 1) 计算 lp_clamped
    lp = math.log(perp + 1.0)
    lp_clamped = max(0.0, min(lp / 10.0, 1.0))

    # 2) 构造 word.x
    glove_vecs = []
    for word in tfidf_vect.get_feature_names_out():
        vec = glove_dict.get(word, torch.zeros(embedding_dim))
        glove_vecs.append(vec)
    data['word'].x = torch.stack(glove_vecs, dim=0)  # (V, D)

    # 3) 构建 text->word 边
    vec = tfidf_vect.transform([text]).tocoo()
    edge_index = torch.stack([
        torch.zeros_like(torch.tensor(vec.col)),
        torch.tensor(vec.col)
    ], dim=0)
    weights = torch.tensor(vec.data * lp_clamped, dtype=torch.float)
    data['text','contains','word'].edge_index = edge_index
    data['text','contains','word'].edge_weight = weights

    # 4) 计算 GloVe 加权平均
    glove_matrix = data['word'].x             # (V, D)
    selected = glove_matrix[vec.col]          # (nnz, D)
    w = weights                              # (nnz,)
    glove_emb = (selected * w.unsqueeze(1)).sum(dim=0) / (w.sum() + 1e-9)  # (D,)

    # 5) 拼接原始特征 + GloVe 特征
    orig = torch.tensor([1.0, lp_clamped], dtype=torch.float, device=glove_emb.device)  # (2,)
    text_feat = torch.cat([orig, glove_emb], dim=0)                                   # (D+2,)
    data['text'].x = text_feat.unsqueeze(0)                                           # (1, D+2)

    # 6) 可选句法边
    if syntax_edges is not None:
        e = torch.tensor(syntax_edges, dtype=torch.long).t()
        data['word','syntax','word'].edge_index = e

    data['text'].y = torch.tensor([item['label']], dtype=torch.long)
    return data


# —————————————————————————————————————————
# 3. 模型封装
# —————————————————————————————————————————
class TextHGTClassifier(nn.Module):
    def __init__(self, metadata, in_dim, hid, n_rels, heads, layers, n_out):
        super().__init__()
        self.gnn = GNN(
            in_dim=in_dim,
            n_hid=hid,
            num_types=len(metadata[0]),
            num_relations=len(metadata[1]),
            n_heads=heads,
            n_layers=layers,
            dropout=0.2
        )
        self.cls = Classifier(hid, n_out)

    def forward(self, data: HeteroData):
        xt = data['text'].x
        xw = data['word'].x

        # 对齐文本和词节点特征维度后再拼接
        feat_dim_text = xt.size(1)
        feat_dim_word = xw.size(1)
        if feat_dim_word != feat_dim_text:
            pad_size = feat_dim_text - feat_dim_word
            if pad_size > 0:
                pad = torch.zeros(xw.size(0), pad_size, device=xw.device)
                xw = torch.cat([pad, xw], dim=1)
            else:
                pad = torch.zeros(xt.size(0), -pad_size, device=xt.device)
                xt = torch.cat([pad, xt], dim=1)

        x = torch.cat([xt, xw], dim=0)

        node_type = torch.cat([
            torch.zeros(xt.size(0), dtype=torch.long, device=xt.device),
            torch.ones(xw.size(0), dtype=torch.long, device=xw.device)
        ], dim=0)

        tw = data['text','contains','word'].edge_index
        ew = torch.cat([tw, tw.flip(0)], dim=1)
        et = torch.zeros(ew.size(1), dtype=torch.long, device=ew.device)
        etime = torch.zeros(ew.size(1), dtype=torch.long, device=ew.device)

        h = self.gnn(x, node_type, etime, ew, et)
        h_text = h[node_type == 0]
        return self.cls(h_text)


# —————————————————————————————————————————
# 4. 训练 与 推理
# —————————————————————————————————————————
def calc_batch_perplexity(texts, pc, batch_size=8):
    perps = []
    for i in tqdm(range(0, len(texts), batch_size), desc="Perplexity"):
        batch = texts[i:i+batch_size]
        enc = pc.tokenizer(
            batch,
            return_tensors='pt',
            truncation=True,
            padding=True,
            max_length=512
        ).to(pc.device)
        with torch.no_grad():
            loss = pc.model(**enc, labels=enc['input_ids']).loss
        p = float(torch.exp(loss))
        perps.extend([p] * len(batch))
    return perps


def train(csv_path, batch_sz=8, epochs=20):
    df = pd.read_csv(csv_path)
    texts  = df['text'].astype(str).tolist()
    labels = df['generated'].astype(int).tolist()
    glove_dict, embedding_dim = load_glove_embeddings('/root/zhihu_ai_scraper_python/glove.6B.100d.txt')

    tfidf = TfidfVectorizer(max_features=5000)
    tfidf.fit(texts)

    pc = PerplexityCalculator(device=DEVICE)
    perps = calc_batch_perplexity(texts, pc, batch_size=batch_sz)

    X_tr, X_rem, y_tr, y_rem, p_tr, p_rem = train_test_split(
        texts, labels, perps, test_size=0.3, stratify=labels, random_state=42)
    X_val, X_te, y_val, y_te, p_val, p_te = train_test_split(
        X_rem, y_rem, p_rem, test_size=0.5, stratify=y_rem, random_state=42)

    def collate_fn(batch):
        return Batch.from_data_list([
            build_hetero_graph(x, tfidf, glove_dict, embedding_dim)
            for x in batch
        ])

    train_loader = DataLoader(
        TextDataset(X_tr, y_tr, p_tr, tfidf),
        batch_size=batch_sz, shuffle=True, collate_fn=collate_fn)
    val_loader   = DataLoader(
        TextDataset(X_val, y_val, p_val, tfidf),
        batch_size=batch_sz, shuffle=False, collate_fn=collate_fn)
    test_loader  = DataLoader(
        TextDataset(X_te, y_te, p_te, tfidf),
        batch_size=batch_sz, shuffle=False, collate_fn=collate_fn)

    model = TextHGTClassifier((['text','word'],[('text','contains','word')]),
                              in_dim=102, hid=256, n_rels=1,
                              heads=2, layers=3, n_out=2).to(DEVICE)
    opt = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(opt, mode='max', patience=2)

    best, patience = 0.0, 0
    for ep in range(1, epochs+1):
        model.train()
        total = 0.0
        for batch in train_loader:
            batch = batch.to(DEVICE)
            logits = model(batch)
            loss = F.cross_entropy(logits, batch['text'].y.view(-1))
            opt.zero_grad(); loss.backward();
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            opt.step()
            total += loss.item()
        print(f"Epoch {ep} loss {total/len(train_loader):.4f}")

        model.eval(); preds, trues = [], []
        with torch.no_grad():
            for batch in val_loader:
                batch = batch.to(DEVICE)
                preds += model(batch).argmax(dim=1).cpu().tolist()
                trues += batch['text'].y.view(-1).cpu().tolist()
        f1 = f1_score(trues, preds, average='macro')
        print(f"Val F1 {f1:.4f}")
        scheduler.step(f1)
        if f1 > best:
            best, patience = f1, 0
            torch.save(model.state_dict(), 'best.pth')
        else:
            patience += 1
            if patience >= 5:
                print("Early stopping.")
                break

    # 测试 & 保存
    model.load_state_dict(torch.load('best.pth', map_location=DEVICE))
    model.eval()
    preds, trues = [], []
    with torch.no_grad():
        for batch in test_loader:
            batch = batch.to(DEVICE)
            preds += model(batch).argmax(dim=1).cpu().tolist()
            trues += batch['text'].y.view(-1).cpu().tolist()
    print("Test F1", f1_score(trues, preds, average='macro'))
    torch.save(model.state_dict(), 'final_model.pth')
    print("Saved final_model.pth")

def train_streaming(csv_path,
                    batch_sz=8,
                    chunk_size=2000,     # 每块读取多少行
                    epochs_per_chunk=1): # 每块数据训练多少 epoch
    # 新增：创建 checkpoints 目录
    ckpt_dir = 'checkpoints'
    os.makedirs(ckpt_dir, exist_ok=True)

    # 1) TF-IDF 预先 fit：用前一小部分数据
    tfidf = TfidfVectorizer(max_features=5000)
    df_sample = pd.read_csv(csv_path, nrows=10000)
    tfidf.fit(df_sample['text'].astype(str))

    # 2) 加载 GloVe / BERT 特征用的参数（已在脚本顶部 load_glove_embeddings）
    glove_dict, embedding_dim = load_glove_embeddings('/root/zhihu_ai_scraper_python/glove.6B.100d.txt')

    # 3) 初始化 Perplexity 计算器
    pc = PerplexityCalculator(device=DEVICE)

    # 4) 初始化模型、优化器、调度器
    model = TextHGTClassifier(
        (['text','word'], [('text','contains','word')]),
        in_dim = embedding_dim+2,
        hid=256, n_rels=1,
        heads=2, layers=3, n_out=2
    ).to(DEVICE)
    opt = torch.optim.Adam(model.parameters(), lr=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(opt, mode='max', patience=2)

    # 5) 定义和原来一样的 collate_fn
    def collate_fn(batch):
        return Batch.from_data_list([
            build_hetero_graph(x, tfidf, glove_dict, embedding_dim)
            for x in batch
        ])

    # 6) 按块读取并训练
    reader = pd.read_csv(csv_path, iterator=True, chunksize=chunk_size)
    val_f1_best = 0.0
    try:
        for chunk_idx, df_chunk in enumerate(reader):
            texts = df_chunk['text'].astype(str).tolist()
            labels = df_chunk['generated'].astype(int).tolist()

            # 6.1) 这块数据先算 perplexities
            perps = []
            for i in range(0, len(texts), batch_sz):
                batch_texts = texts[i:i + batch_sz]
                perps.extend(calc_batch_perplexity(batch_texts, pc, batch_size=len(batch_texts)))

            # 6.2) 构造 DataLoader，马上对这一块做 online 训练
            dataset = TextDataset(texts, labels, perps, tfidf)
            loader = DataLoader(dataset,
                                batch_size=batch_sz,
                                shuffle=True,
                                collate_fn=collate_fn)
            model.train()
            for _ in range(epochs_per_chunk):
                for batch in loader:
                    batch = batch.to(DEVICE)
                    logits = model(batch)
                    loss = F.cross_entropy(logits, batch['text'].y.view(-1))
                    opt.zero_grad()
                    loss.backward()
                    opt.step()

            # 6.3) （可选）在每块后做一次验证，调整 lr
            # val_f1 = validate(model, val_loader)
            # scheduler.step(val_f1)
            # if val_f1 > val_f1_best:
            #     val_f1_best = val_f1
            #     torch.save(model.state_dict(), 'best_stream.pth')

            print(f"Chunk {chunk_idx} trained, size={len(texts)} samples.")

            # —— 新增：保存当前模型状态 ——
            ckpt_path = os.path.join(ckpt_dir, f'chunk_{chunk_idx}.pth')
            torch.save(model.state_dict(), ckpt_path)
    except KeyboardInterrupt:
        # 中断时立刻保存
        torch.save(model.state_dict(), os.path.join(ckpt_dir, 'interrupt.pth'))
        print("训练被中断，模型已保存到 checkpoints/interrupt.pth")
        raise

    # 最后保存模型
    torch.save(model.state_dict(), 'final_model.pth')
    print("Streaming training done.")


# 1) 读全表 fit TF–IDF
df_full = pd.read_csv('/root/zhihu_ai_scraper_python/AI_Human.csv')
tfidf    = TfidfVectorizer(max_features=5000)
tfidf.fit(df_full['text'].astype(str))

# 2) 加载 GloVe
glove_dict, embedding_dim = load_glove_embeddings(
    '/root/zhihu_ai_scraper_python/glove.6B.100d.txt'
)

# 3) 初始化 perplexity 计算器
pc = PerplexityCalculator(device=DEVICE)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--mode', choices=['train','infer','eval'], default='train')
    parser.add_argument('--csv',  default='/root/zhihu_ai_scraper_python/test.csv')
    parser.add_argument('--texts', nargs='*', help='要预测的文本列表')
    args = parser.parse_args()

    # —— 1) 流式训练 ——
    if args.mode == 'train':
        train_streaming(
            csv_path=args.csv,
            batch_sz=8,
            chunk_size=2000,
            epochs_per_chunk=1
        )

    # —— 2) 批量推理 ——
    elif args.mode == 'infer':
        if not args.texts:
            print("请通过 --texts 指定要预测的文本列表")
        else:
            model = TextHGTClassifier(
                (['text','word'], [('text','contains','word')]),
                in_dim=embedding_dim+2,
                hid=256, n_rels=1, heads=2, layers=3, n_out=2
            ).to(DEVICE)
            model.load_state_dict(torch.load('final_model.pth', map_location=DEVICE))
            model.eval()

            graphs = [
                build_hetero_graph(
                    {'text': t, 'label': 0, 'perp': 1.0},
                    tfidf, glove_dict, embedding_dim
                )
                for t in args.texts
            ]
            batch = Batch.from_data_list(graphs).to(DEVICE)
            with torch.no_grad():
                preds = model(batch).argmax(dim=1).cpu().tolist()
            for txt, p in zip(args.texts, preds):
                print(f"文本：{txt}\n预测标签：{p}")

    # —— 3) 测试集评估 ——
    else:  # args.mode == 'eval'
        # —— 1) 加载模型 ——
        model = TextHGTClassifier(
            (['text', 'word'], [('text', 'contains', 'word')]),
            in_dim=embedding_dim + 2,
            hid=128, n_rels=1, heads=2, layers=2, n_out=2
        ).to(DEVICE)
        model.load_state_dict(torch.load('final_model.pth', map_location=DEVICE))
        model.eval()

        # —— 2) 随机抽取 10000 条样本 ——
        df_eval = df_full.sample(n=10000, random_state=42).reset_index(drop=True)
        X_te = df_eval['text'].astype(str).tolist()
        y_te = df_eval['generated'].astype(int).tolist()

        # —— 3) 重新计算这些样本的 Perplexity ——
        perps_te = calc_batch_perplexity(X_te, pc, batch_size=8)


        # —— 4) 构造 DataLoader ——
        def collate_fn_eval(batch):
            return Batch.from_data_list([
                build_hetero_graph(x, tfidf, glove_dict, embedding_dim)
                for x in batch
            ])


        test_dataset = TextDataset(X_te, y_te, perps_te, tfidf)
        test_loader = DataLoader(
            test_dataset, batch_size=8, shuffle=False,
            collate_fn=collate_fn_eval, num_workers=4
        )

        # —— 5) 批量推理 & 打印指标 ——
        from sklearn.metrics import f1_score, precision_score, recall_score

        preds, trues = [], []
        for batch in test_loader:
            batch = batch.to(DEVICE)
            logits = model(batch)
            preds += logits.argmax(dim=1).cpu().tolist()
            trues += batch['text'].y.view(-1).cpu().tolist()

        print("Eval on 10000 random samples:")
        print("Test F1:    ", f1_score(trues, preds, average='macro'))
        print("Precision:  ", precision_score(trues, preds, average='macro'))
        print("Recall:     ", recall_score(trues, preds, average='macro'))

"""
训练的英语
Test F1: 0.8468282103888372
Precision: 0.8903168130077068
Recall: 0.829152856383631
2025.7.4
"""